<!-- 现代化AI智能推荐图标 -->
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EE5359;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF6B70;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EE5359;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 主圆形背景 -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="2" filter="url(#glow)"/>

  <!-- AI大脑图案 -->
  <g fill="white" opacity="0.95">
    <!-- 大脑轮廓 -->
    <path d="M20 28 Q20 20 28 20 Q32 16 36 20 Q44 20 44 28 Q44 32 42 36 Q40 40 36 42 Q32 44 28 42 Q24 40 22 36 Q20 32 20 28 Z" fill="white" opacity="0.2"/>

    <!-- 神经网络节点 -->
    <circle cx="26" cy="26" r="2.5" fill="white"/>
    <circle cx="38" cy="26" r="2.5" fill="white"/>
    <circle cx="32" cy="32" r="2" fill="white"/>
    <circle cx="28" cy="38" r="1.5" fill="white" opacity="0.8"/>
    <circle cx="36" cy="38" r="1.5" fill="white" opacity="0.8"/>

    <!-- 连接线 -->
    <path d="M26 26 L32 32 L38 26" stroke="white" stroke-width="1.5" fill="none" opacity="0.7"/>
    <path d="M32 32 L28 38" stroke="white" stroke-width="1" fill="none" opacity="0.6"/>
    <path d="M32 32 L36 38" stroke="white" stroke-width="1" fill="none" opacity="0.6"/>

    <!-- AI文字 -->
    <text x="32" y="52" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="white">AI</text>
  </g>

  <!-- 装饰性光点 -->
  <circle cx="22" cy="22" r="1" fill="white" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="42" cy="22" r="1" fill="white" opacity="0.6">
    <animate attributeName="opacity" values="1;0.6;1" dur="2s" repeatCount="indefinite"/>
  </circle>
</svg>
